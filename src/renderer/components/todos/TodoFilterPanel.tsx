import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  ChevronDown,
  Calendar,
  Flag,
  Tag,
  CheckCircle,
  Circle,
  RotateCcw,
  Folder
} from 'lucide-react';
import { TodoStatus, TodoPriority, Category } from '@shared/types';
import { TodoFilters, FilterPreset } from '@renderer/hooks/useTodoFilters';

export interface TodoFilterPanelProps {
  isOpen: boolean;
  onClose: () => void;
  filters: TodoFilters;
  onUpdateFilter: <K extends keyof TodoFilters>(key: K, value: TodoFilters[K]) => void;
  onApplyPreset: (presetId: string) => void;
  onClearFilters: () => void;
  presets: FilterPreset[];
  activePreset: string;
  filterOptions: {
    statuses: TodoStatus[];
    priorities: TodoPriority[];
    tags: string[];
    categories: Category[];
  };
  filterSummary: {
    activeFilters: string[];
    hasActiveFilters: boolean;
    totalFiltered: number;
    totalOriginal: number;
  };
}

const STATUS_LABELS: Record<TodoStatus, string> = {
  pending: 'Pending',
  in_progress: 'In Progress',
  completed: 'Completed',
  archived: 'Archived',
  cancelled: 'Cancelled',
};

const PRIORITY_LABELS: Record<TodoPriority, string> = {
  very_low: 'Very Low',
  low: 'Low',
  medium: 'Medium',
  high: 'High',
  very_high: 'Very High',
};

const DUE_DATE_OPTIONS = [
  { value: 'all', label: 'All' },
  { value: 'overdue', label: 'Overdue' },
  { value: 'today', label: 'Due Today' },
  { value: 'tomorrow', label: 'Due Tomorrow' },
  { value: 'this_week', label: 'This Week' },
  { value: 'this_month', label: 'This Month' },
  { value: 'no_due_date', label: 'No Due Date' },
];

export const TodoFilterPanel: React.FC<TodoFilterPanelProps> = ({
  isOpen,
  onClose,
  filters,
  onUpdateFilter,
  onApplyPreset,
  onClearFilters,
  presets,
  activePreset,
  filterOptions,
  filterSummary,
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['presets', 'status', 'priority', 'categories'])
  );

  const toggleSection = (section: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(section)) {
        newSet.delete(section);
      } else {
        newSet.add(section);
      }
      return newSet;
    });
  };

  const handleStatusToggle = (status: TodoStatus) => {
    const newStatuses = filters.status.includes(status)
      ? filters.status.filter(s => s !== status)
      : [...filters.status, status];
    onUpdateFilter('status', newStatuses);
  };

  const handlePriorityToggle = (priority: TodoPriority) => {
    const newPriorities = filters.priority.includes(priority)
      ? filters.priority.filter(p => p !== priority)
      : [...filters.priority, priority];
    onUpdateFilter('priority', newPriorities);
  };

  const handleTagToggle = (tag: string) => {
    const newTags = filters.tags.includes(tag)
      ? filters.tags.filter(t => t !== tag)
      : [...filters.tags, tag];
    onUpdateFilter('tags', newTags);
  };

  const handleCategoryToggle = (categoryId: string) => {
    const newCategories = filters.category.includes(categoryId)
      ? filters.category.filter(c => c !== categoryId)
      : [...filters.category, categoryId];
    onUpdateFilter('category', newCategories);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, x: 300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 300 }}
          transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          className="absolute right-0 top-0 h-full w-96 fa-glass-panel-frosted border-l border-fa-gray-200 overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="sticky top-0 bg-fa-white-glass backdrop-blur-md border-b border-fa-gray-200 p-6">
            <div className="flex items-center justify-between">
              <h2 className="fa-heading-2">Filters</h2>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={onClose}
                className="p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass"
              >
                <X className="w-5 h-5" />
              </motion.button>
            </div>

            {/* Filter Summary */}
            <div className="mt-4 p-3 bg-fa-blue-50 rounded-lg">
              <div className="flex items-center justify-between text-sm">
                <span className="text-fa-gray-600">
                  Showing {filterSummary.totalFiltered} of {filterSummary.totalOriginal} tasks
                </span>
                {filterSummary.hasActiveFilters && (
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={onClearFilters}
                    className="flex items-center space-x-1 text-fa-blue-600 hover:text-fa-blue-700"
                  >
                    <RotateCcw className="w-3 h-3" />
                    <span>Clear</span>
                  </motion.button>
                )}
              </div>
            </div>
          </div>

          <div className="p-6 space-y-6">
            {/* Filter Presets */}
            <div>
              <button
                onClick={() => toggleSection('presets')}
                className="flex items-center justify-between w-full text-left"
              >
                <h3 className="fa-heading-3">Quick Filters</h3>
                <ChevronDown className={`w-4 h-4 transition-transform ${
                  expandedSections.has('presets') ? 'rotate-180' : ''
                }`} />
              </button>
              
              <AnimatePresence>
                {expandedSections.has('presets') && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="mt-3 space-y-2"
                  >
                    {presets.map((preset) => (
                      <motion.button
                        key={preset.id}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => onApplyPreset(preset.id)}
                        className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
                          activePreset === preset.id
                            ? 'bg-fa-blue-100 border border-fa-blue-300 text-fa-blue-800'
                            : 'bg-fa-white-glass hover:bg-fa-gray-50 border border-fa-gray-200'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <span className="text-lg">{preset.icon}</span>
                          <div>
                            <div className="font-medium">{preset.name}</div>
                          </div>
                        </div>
                      </motion.button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Status Filter */}
            <div>
              <button
                onClick={() => toggleSection('status')}
                className="flex items-center justify-between w-full text-left"
              >
                <h3 className="fa-heading-3 flex items-center">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Status
                </h3>
                <ChevronDown className={`w-4 h-4 transition-transform ${
                  expandedSections.has('status') ? 'rotate-180' : ''
                }`} />
              </button>
              
              <AnimatePresence>
                {expandedSections.has('status') && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="mt-3 space-y-2"
                  >
                    {filterOptions.statuses.map((status) => (
                      <motion.button
                        key={status}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => handleStatusToggle(status)}
                        className={`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${
                          filters.status.includes(status)
                            ? 'bg-fa-blue-100 text-fa-blue-800'
                            : 'hover:bg-fa-gray-50'
                        }`}
                      >
                        {filters.status.includes(status) ? (
                          <CheckCircle className="w-4 h-4 text-fa-blue-600" />
                        ) : (
                          <Circle className="w-4 h-4 text-fa-gray-400" />
                        )}
                        <span>{STATUS_LABELS[status]}</span>
                      </motion.button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Priority Filter */}
            <div>
              <button
                onClick={() => toggleSection('priority')}
                className="flex items-center justify-between w-full text-left"
              >
                <h3 className="fa-heading-3 flex items-center">
                  <Flag className="w-4 h-4 mr-2" />
                  Priority
                </h3>
                <ChevronDown className={`w-4 h-4 transition-transform ${
                  expandedSections.has('priority') ? 'rotate-180' : ''
                }`} />
              </button>
              
              <AnimatePresence>
                {expandedSections.has('priority') && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="mt-3 space-y-2"
                  >
                    {filterOptions.priorities.map((priority) => (
                      <motion.button
                        key={priority}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => handlePriorityToggle(priority)}
                        className={`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${
                          filters.priority.includes(priority)
                            ? 'bg-fa-blue-100 text-fa-blue-800'
                            : 'hover:bg-fa-gray-50'
                        }`}
                      >
                        {filters.priority.includes(priority) ? (
                          <CheckCircle className="w-4 h-4 text-fa-blue-600" />
                        ) : (
                          <Circle className="w-4 h-4 text-fa-gray-400" />
                        )}
                        <span>{PRIORITY_LABELS[priority]}</span>
                      </motion.button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Due Date Filter */}
            <div>
              <button
                onClick={() => toggleSection('dueDate')}
                className="flex items-center justify-between w-full text-left"
              >
                <h3 className="fa-heading-3 flex items-center">
                  <Calendar className="w-4 h-4 mr-2" />
                  Due Date
                </h3>
                <ChevronDown className={`w-4 h-4 transition-transform ${
                  expandedSections.has('dueDate') ? 'rotate-180' : ''
                }`} />
              </button>
              
              <AnimatePresence>
                {expandedSections.has('dueDate') && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="mt-3 space-y-2"
                  >
                    {DUE_DATE_OPTIONS.map((option) => (
                      <motion.button
                        key={option.value}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => onUpdateFilter('dueDate', option.value as any)}
                        className={`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${
                          filters.dueDate === option.value
                            ? 'bg-fa-blue-100 text-fa-blue-800'
                            : 'hover:bg-fa-gray-50'
                        }`}
                      >
                        {filters.dueDate === option.value ? (
                          <CheckCircle className="w-4 h-4 text-fa-blue-600" />
                        ) : (
                          <Circle className="w-4 h-4 text-fa-gray-400" />
                        )}
                        <span>{option.label}</span>
                      </motion.button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Categories Filter */}
            {filterOptions.categories.length > 0 && (
              <div>
                <button
                  onClick={() => toggleSection('categories')}
                  className="flex items-center justify-between w-full text-left"
                >
                  <h3 className="fa-heading-3 flex items-center">
                    <Folder className="w-4 h-4 mr-2" />
                    Categories
                  </h3>
                  <ChevronDown className={`w-4 h-4 transition-transform ${
                    expandedSections.has('categories') ? 'rotate-180' : ''
                  }`} />
                </button>

                <AnimatePresence>
                  {expandedSections.has('categories') && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      className="mt-3 space-y-2"
                    >
                      {filterOptions.categories.map((category) => (
                        <motion.button
                          key={category.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => handleCategoryToggle(category.id)}
                          className={`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${
                            filters.category.includes(category.id)
                              ? 'bg-fa-blue-100 text-fa-blue-800'
                              : 'hover:bg-fa-gray-50'
                          }`}
                        >
                          {filters.category.includes(category.id) ? (
                            <CheckCircle className="w-4 h-4 text-fa-blue-600" />
                          ) : (
                            <Circle className="w-4 h-4 text-fa-gray-400" />
                          )}
                          <div
                            className="w-3 h-3 rounded"
                            style={{ backgroundColor: category.color }}
                          />
                          <span>{category.name}</span>
                        </motion.button>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}

            {/* Tags Filter */}
            {filterOptions.tags.length > 0 && (
              <div>
                <button
                  onClick={() => toggleSection('tags')}
                  className="flex items-center justify-between w-full text-left"
                >
                  <h3 className="fa-heading-3 flex items-center">
                    <Tag className="w-4 h-4 mr-2" />
                    Tags
                  </h3>
                  <ChevronDown className={`w-4 h-4 transition-transform ${
                    expandedSections.has('tags') ? 'rotate-180' : ''
                  }`} />
                </button>
                
                <AnimatePresence>
                  {expandedSections.has('tags') && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      className="mt-3 space-y-2"
                    >
                      {filterOptions.tags.map((tag) => (
                        <motion.button
                          key={tag}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => handleTagToggle(tag)}
                          className={`w-full text-left p-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${
                            filters.tags.includes(tag)
                              ? 'bg-fa-blue-100 text-fa-blue-800'
                              : 'hover:bg-fa-gray-50'
                          }`}
                        >
                          {filters.tags.includes(tag) ? (
                            <CheckCircle className="w-4 h-4 text-fa-blue-600" />
                          ) : (
                            <Circle className="w-4 h-4 text-fa-gray-400" />
                          )}
                          <span>{tag}</span>
                        </motion.button>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
